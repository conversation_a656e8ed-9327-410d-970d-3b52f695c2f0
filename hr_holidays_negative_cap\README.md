# HR Holidays Negative Cap Module

## Overview

This module extends Odoo's Time Off system to allow employees to take time off even when they don't have sufficient balance, up to a configurable negative limit per leave type.

## Features

### 🔧 Configuration
- **Allow Negative Cap**: Enable/disable negative balance for each time off type
- **Negative Cap Days**: Set maximum negative days allowed (e.g., up to -5 days)
- **Per Leave Type**: Different negative caps can be set for different leave types

### ✅ Validation
- Prevents requests that exceed the configured negative cap limit
- Clear error messages when limits are exceeded
- Maintains data integrity while providing flexibility

### 📊 User Interface
- Visual indicators when negative cap is enabled
- Warnings when requests will result in negative balance
- Additional fields in tree and form views
- Search filters for negative cap requests

### 🔍 Monitoring
- Track which requests use negative cap
- Filter and report on negative balance usage
- Audit trail through message posting

## Installation

1. Copy the `hr_holidays_negative_cap` folder to your Odoo addons directory
2. Update the apps list in Odoo
3. Install the "HR Holidays Negative Cap" module
4. The module will automatically extend existing Time Off functionality

## Configuration

### Setting up Negative Cap for Leave Types

1. Go to **Time Off > Configuration > Time Off Types**
2. Open the leave type you want to configure
3. In the **Negative Cap Configuration** section:
   - Enable **Allow Negative Cap**
   - Set **Negative Cap (Days)** to the maximum negative balance allowed
4. Save the configuration

### Example Configurations

- **Annual Leave**: Allow up to -5 days negative balance
- **Sick Leave**: Allow up to -3 days negative balance  
- **Personal Leave**: No negative balance allowed (keep disabled)

## Usage

### For Employees
- Request time off as usual
- System will show warnings if the request will result in negative balance
- Requests within negative cap limits will be processed normally

### For Managers
- Approve/reject requests as usual
- System prevents approval of requests exceeding negative cap limits
- Clear visibility of which requests use negative balance

### For HR Officers
- Monitor negative balance usage through filters
- Configure negative cap limits per leave type
- Generate reports on negative balance trends

## Technical Details

### Models Extended
- `hr.leave.type`: Added negative cap configuration fields
- `hr.leave`: Added validation and computed fields

### New Fields
- `allow_negative_cap`: Boolean field to enable negative cap
- `negative_cap_days`: Float field for maximum negative days
- `effective_remaining_days`: Computed field including negative cap
- `will_use_negative_cap`: Computed field indicating negative balance usage

### Validation Logic
- Checks current balance vs. requested days
- Compares against negative cap limit
- Provides detailed error messages
- Maintains existing validation for non-negative cap types

## Arabic Support

هذا المديوول يدعم اللغة العربية ويوفر:
- السماح بالرصيد السلبي للإجازات
- تحديد حد أقصى للأيام السلبية المسموح بها
- التحقق من صحة الطلبات
- رسائل تحذيرية واضحة

## Compatibility

- Odoo 15.0+
- Compatible with existing Time Off modules
- Works with custom leave type configurations
- Maintains backward compatibility

## Support

For support and customization requests, please contact your system administrator or the module developer.

## License

LGPL-3 License - See LICENSE file for details.
