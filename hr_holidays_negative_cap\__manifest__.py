# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

{
    'name': 'HR Holidays Negative Cap',
    'version': '1.0.0',
    'category': 'Human Resources/Time Off',
    'sequence': 95,
    'summary': 'Allow negative balance for time off with configurable limits',
    'description': """
HR Holidays Negative Cap
========================

This module extends the Time Off system to allow employees to take time off 
even when they don't have sufficient balance, up to a configurable negative limit.

Key Features:
- Enable/disable negative cap per time off type
- Set maximum negative days allowed (e.g., up to -5 days)
- Validation to prevent exceeding the negative limit
- Clear indication in the interface when negative balance is used

Configuration:
- Go to Time Off > Configuration > Time Off Types
- Enable "Allow Negative Cap" for the desired time off types
- Set the maximum negative days allowed
- The system will validate requests against this limit

Arabic Support:
- السماح بالرصيد السلبي للإجازات مع حدود قابلة للتكوين
- تحديد عدد الأيام السلبية المسموح بها لكل نوع إجازة
- التحقق من صحة الطلبات ضد هذه الحدود
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': [
        'hr_holidays',
        'hr',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/hr_leave_type_views.xml',
    ],
    'demo': [],
    'test': [
        'tests/test_negative_cap.py',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
