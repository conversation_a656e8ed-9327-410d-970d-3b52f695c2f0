#!/bin/bash
# Setup guide for HR Holidays Negative Cap module

echo "🚀 HR Holidays Negative Cap Module Setup"
echo "========================================"

# Function to check if Odo<PERSON> is running
check_odoo_running() {
    if pgrep -f "odoo" > /dev/null; then
        echo "✅ Odoo is running"
        return 0
    else
        echo "❌ Odoo is not running"
        return 1
    fi
}

# Function to find Odoo addons directory
find_addons_dir() {
    local possible_dirs=(
        "/opt/odoo/addons"
        "/usr/lib/python3/dist-packages/odoo/addons"
        "/var/lib/odoo/addons"
        "~/odoo/addons"
        "./addons"
    )
    
    for dir in "${possible_dirs[@]}"; do
        if [ -d "$dir" ]; then
            echo "📁 Found addons directory: $dir"
            return 0
        fi
    done
    
    echo "❌ Could not find Odoo addons directory"
    echo "Please specify the path manually:"
    read -p "Enter addons directory path: " custom_dir
    if [ -d "$custom_dir" ]; then
        echo "📁 Using custom directory: $custom_dir"
        return 0
    else
        echo "❌ Directory does not exist: $custom_dir"
        return 1
    fi
}

# Main setup function
main() {
    echo "Step 1: Checking Odoo installation..."
    if ! check_odoo_running; then
        echo "Please start Odoo first, then run this script again."
        exit 1
    fi
    
    echo ""
    echo "Step 2: Finding addons directory..."
    if ! find_addons_dir; then
        echo "Cannot proceed without addons directory."
        exit 1
    fi
    
    echo ""
    echo "Step 3: Module validation..."
    if [ -f "quick_test.py" ]; then
        python3 quick_test.py
        if [ $? -ne 0 ]; then
            echo "Module validation failed. Please fix issues first."
            exit 1
        fi
    else
        echo "⚠️  quick_test.py not found, skipping validation"
    fi
    
    echo ""
    echo "Step 4: Installation instructions..."
    echo "1. Copy this module directory to your Odoo addons directory"
    echo "2. Restart Odoo service:"
    echo "   sudo systemctl restart odoo"
    echo "   # or"
    echo "   sudo service odoo restart"
    echo ""
    echo "3. In Odoo web interface:"
    echo "   - Go to Apps"
    echo "   - Click 'Update Apps List'"
    echo "   - Search for 'HR Holidays Negative Cap'"
    echo "   - Click Install"
    echo ""
    echo "Step 5: Configuration..."
    echo "After installation:"
    echo "1. Go to Time Off > Configuration > Time Off Types"
    echo "2. Edit the leave types you want to enable negative cap for"
    echo "3. Enable 'Allow Negative Cap' and set 'Negative Cap Days'"
    echo "4. Save the configuration"
    echo ""
    echo "🎉 Setup guide completed!"
    echo "For detailed instructions, see INSTALLATION.md"
}

# Run main function
main
