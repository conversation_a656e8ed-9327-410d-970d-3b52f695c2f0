# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from datetime import date, timedelta


class TestNegativeCap(TransactionCase):
    
    def setUp(self):
        super().setUp()
        
        # Create test employee
        self.employee = self.env['hr.employee'].create({
            'name': 'Test Employee',
            'work_email': '<EMAIL>',
        })
        
        # Create leave type with negative cap
        self.leave_type_with_cap = self.env['hr.leave.type'].create({
            'name': 'Annual Leave with Negative Cap',
            'requires_allocation': 'yes',
            'allow_negative_cap': True,
            'negative_cap_days': 5.0,
        })
        
        # Create leave type without negative cap
        self.leave_type_without_cap = self.env['hr.leave.type'].create({
            'name': 'Sick Leave without Negative Cap',
            'requires_allocation': 'yes',
            'allow_negative_cap': False,
            'negative_cap_days': 0.0,
        })
        
        # Create allocation for employee (10 days)
        self.env['hr.leave.allocation'].create({
            'name': 'Test Allocation',
            'employee_id': self.employee.id,
            'holiday_status_id': self.leave_type_with_cap.id,
            'number_of_days': 10.0,
            'state': 'validate',
        })
        
        # Create allocation for sick leave (5 days)
        self.env['hr.leave.allocation'].create({
            'name': 'Sick Leave Allocation',
            'employee_id': self.employee.id,
            'holiday_status_id': self.leave_type_without_cap.id,
            'number_of_days': 5.0,
            'state': 'validate',
        })

    def test_negative_cap_configuration_validation(self):
        """Test validation of negative cap configuration"""
        
        # Test: negative cap days cannot be negative
        with self.assertRaises(ValidationError):
            self.leave_type_with_cap.write({
                'allow_negative_cap': True,
                'negative_cap_days': -3.0,
            })
    
    def test_leave_request_within_negative_cap_limit(self):
        """Test leave request within negative cap limit should be allowed"""
        
        # Request 12 days (10 available + 2 negative, within 5 days cap)
        leave_request = self.env['hr.leave'].create({
            'name': 'Test Leave Within Cap',
            'employee_id': self.employee.id,
            'holiday_status_id': self.leave_type_with_cap.id,
            'date_from': date.today(),
            'date_to': date.today() + timedelta(days=11),
            'number_of_days': 12.0,
        })
        
        # Should be able to confirm and validate
        leave_request.action_confirm()
        leave_request.action_validate()
        
        self.assertEqual(leave_request.state, 'validate')
        self.assertTrue(leave_request.will_use_negative_cap)

    def test_leave_request_exceeding_negative_cap_limit(self):
        """Test leave request exceeding negative cap limit should be rejected"""
        
        # Request 20 days (10 available + 10 negative, exceeds 5 days cap)
        leave_request = self.env['hr.leave'].create({
            'name': 'Test Leave Exceeding Cap',
            'employee_id': self.employee.id,
            'holiday_status_id': self.leave_type_with_cap.id,
            'date_from': date.today(),
            'date_to': date.today() + timedelta(days=19),
            'number_of_days': 20.0,
        })
        
        # Should be able to confirm but not validate
        leave_request.action_confirm()
        
        with self.assertRaises(ValidationError):
            leave_request.action_validate()

    def test_leave_request_without_negative_cap(self):
        """Test leave request for type without negative cap"""
        
        # Request 7 days (5 available + 2 over, no negative cap allowed)
        leave_request = self.env['hr.leave'].create({
            'name': 'Test Leave No Cap',
            'employee_id': self.employee.id,
            'holiday_status_id': self.leave_type_without_cap.id,
            'date_from': date.today(),
            'date_to': date.today() + timedelta(days=6),
            'number_of_days': 7.0,
        })
        
        leave_request.action_confirm()
        
        # Should raise validation error
        with self.assertRaises(ValidationError):
            leave_request.action_validate()

    def test_computed_fields(self):
        """Test computed fields are calculated correctly"""
        
        leave_request = self.env['hr.leave'].create({
            'name': 'Test Computed Fields',
            'employee_id': self.employee.id,
            'holiday_status_id': self.leave_type_with_cap.id,
            'date_from': date.today(),
            'date_to': date.today() + timedelta(days=11),
            'number_of_days': 12.0,
        })
        
        # Check computed fields
        self.assertTrue(leave_request.allow_negative_cap)
        self.assertEqual(leave_request.negative_cap_days, 5.0)
        self.assertTrue(leave_request.will_use_negative_cap)

    def test_get_employees_days_with_negative_cap(self):
        """Test get_employees_days method includes negative cap information"""
        
        result = self.leave_type_with_cap.get_employees_days([self.employee.id])
        employee_data = result[self.leave_type_with_cap.id][self.employee.id]
        
        self.assertTrue(employee_data['allow_negative_cap'])
        self.assertEqual(employee_data['negative_cap_allowed'], 5.0)
        self.assertEqual(employee_data['effective_remaining'], 15.0)  # 10 + 5

    def test_onchange_allow_negative_cap(self):
        """Test onchange method resets negative cap days when disabled"""
        
        # Enable negative cap and set days
        self.leave_type_without_cap.write({
            'allow_negative_cap': True,
            'negative_cap_days': 3.0,
        })
        
        # Disable negative cap
        self.leave_type_without_cap.write({
            'allow_negative_cap': False,
        })
        
        # Should reset negative cap days to 0
        self.assertEqual(self.leave_type_without_cap.negative_cap_days, 0.0)
