# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from odoo.tools.float_utils import float_compare


class HrLeave(models.Model):
    _inherit = 'hr.leave'

    # Computed fields for negative cap information
    allow_negative_cap = fields.Boolean(
        related='holiday_status_id.allow_negative_cap',
        string='Allow Negative Cap',
        readonly=True
    )
    
    negative_cap_days = fields.Float(
        related='holiday_status_id.negative_cap_days',
        string='Negative Cap Days',
        readonly=True
    )
    
    effective_remaining_days = fields.Float(
        string='Effective Remaining Days',
        compute='_compute_effective_remaining_days',
        help='Remaining days including negative cap allowance'
    )
    
    will_use_negative_cap = fields.Boolean(
        string='Will Use Negative Cap',
        compute='_compute_will_use_negative_cap',
        store=True,
        help='Indicates if this request will result in negative balance'
    )

    @api.depends('employee_id', 'holiday_status_id', 'number_of_days')
    def _compute_effective_remaining_days(self):
        """Compute effective remaining days including negative cap"""
        for leave in self:
            if leave.employee_id and leave.holiday_status_id:
                # Get current balance
                leave_days = leave.holiday_status_id.get_employees_days([leave.employee_id.id])
                employee_data = leave_days.get(leave.holiday_status_id.id, {}).get(leave.employee_id.id, {})
                
                remaining = employee_data.get('remaining_leaves', 0)
                negative_cap = employee_data.get('negative_cap_allowed', 0)
                
                leave.effective_remaining_days = remaining + negative_cap
            else:
                leave.effective_remaining_days = 0.0

    @api.depends('employee_id', 'holiday_status_id', 'number_of_days', 'state')
    def _compute_will_use_negative_cap(self):
        """Check if this request will use negative cap"""
        for leave in self:
            if leave.employee_id and leave.holiday_status_id and leave.number_of_days:
                # Get current balance
                leave_days = leave.holiday_status_id.get_employees_days([leave.employee_id.id])
                employee_data = leave_days.get(leave.holiday_status_id.id, {}).get(leave.employee_id.id, {})
                
                remaining = employee_data.get('remaining_leaves', 0)
                
                # Check if request will result in negative balance
                leave.will_use_negative_cap = float_compare(
                    remaining, leave.number_of_days, precision_digits=2
                ) < 0
            else:
                leave.will_use_negative_cap = False

    @api.constrains('number_of_days', 'employee_id', 'holiday_status_id', 'state')
    def _check_negative_cap_limit(self):
        """Validate that leave request doesn't exceed negative cap limit"""
        for leave in self:
            if (leave.state in ['confirm', 'validate1', 'validate'] and 
                leave.employee_id and leave.holiday_status_id and 
                leave.number_of_days > 0):
                
                # Get employee's leave data
                leave_days = leave.holiday_status_id.get_employees_days([leave.employee_id.id])
                employee_data = leave_days.get(leave.holiday_status_id.id, {}).get(leave.employee_id.id, {})
                
                remaining = employee_data.get('remaining_leaves', 0)
                allow_negative = employee_data.get('allow_negative_cap', False)
                negative_cap = employee_data.get('negative_cap_allowed', 0)
                
                # Calculate balance after this request
                balance_after_request = remaining - leave.number_of_days
                
                # Check if negative cap is exceeded
                if allow_negative:
                    min_allowed_balance = -negative_cap
                    if float_compare(balance_after_request, min_allowed_balance, precision_digits=2) < 0:
                        raise ValidationError(_(
                            'Cannot approve this leave request. '
                            'Employee %(employee)s would exceed the negative cap limit of %(limit)s days '
                            'for leave type "%(leave_type)s". '
                            'Current balance: %(current)s days, '
                            'Requested: %(requested)s days, '
                            'Balance after request: %(after)s days.'
                        ) % {
                            'employee': leave.employee_id.name,
                            'limit': negative_cap,
                            'leave_type': leave.holiday_status_id.name,
                            'current': remaining,
                            'requested': leave.number_of_days,
                            'after': balance_after_request
                        })
                else:
                    # Standard validation for non-negative cap leave types
                    if float_compare(balance_after_request, 0, precision_digits=2) < 0:
                        raise ValidationError(_(
                            'Cannot approve this leave request. '
                            'Employee %(employee)s does not have sufficient balance '
                            'for leave type "%(leave_type)s". '
                            'Current balance: %(current)s days, '
                            'Requested: %(requested)s days.'
                        ) % {
                            'employee': leave.employee_id.name,
                            'leave_type': leave.holiday_status_id.name,
                            'current': remaining,
                            'requested': leave.number_of_days
                        })

    def action_confirm(self):
        """Override to show warning when using negative cap"""
        result = super().action_confirm()
        
        # Show warning for requests that will use negative cap
        negative_cap_requests = self.filtered('will_use_negative_cap')
        if negative_cap_requests:
            message = _('Warning: The following leave requests will result in negative balance:\n')
            for leave in negative_cap_requests:
                message += _('- %s: %s days for %s\n') % (
                    leave.employee_id.name,
                    leave.number_of_days,
                    leave.holiday_status_id.name
                )
            
            # Log the warning
            for leave in negative_cap_requests:
                leave.message_post(
                    body=_('This leave request will result in negative balance. '
                          'Negative cap is enabled for this leave type.'),
                    message_type='comment'
                )
        
        return result
