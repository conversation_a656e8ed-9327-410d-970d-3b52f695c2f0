# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_negative_cap
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-21 12:00+0000\n"
"PO-Revision-Date: 2025-01-21 12:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: hr_holidays_negative_cap
#: model:ir.model.fields,field_description:hr_holidays_negative_cap.field_hr_leave_type__allow_negative_cap
msgid "Allow Negative Cap"
msgstr "السماح بالسقف السلبي"

#. module: hr_holidays_negative_cap
#: model:ir.model.fields,help:hr_holidays_negative_cap.field_hr_leave_type__allow_negative_cap
msgid "Allow employees to take time off even when they don't have sufficient balance, up to the specified negative limit."
msgstr "السماح للموظفين بأخذ إجازة حتى لو لم يكن لديهم رصيد كافي، حتى الحد السلبي المحدد."

#. module: hr_holidays_negative_cap
#: model:ir.model.fields,field_description:hr_holidays_negative_cap.field_hr_leave_type__negative_cap_days
msgid "Negative Cap (Days)"
msgstr "السقف السلبي (أيام)"

#. module: hr_holidays_negative_cap
#: model:ir.model.fields,help:hr_holidays_negative_cap.field_hr_leave_type__negative_cap_days
msgid "Maximum number of days that can be taken as negative balance. For example, if set to 5, employees can have up to -5 days balance."
msgstr "العدد الأقصى من الأيام التي يمكن أخذها كرصيد سلبي. على سبيل المثال، إذا تم تعيينها إلى 5، يمكن للموظفين أن يكون لديهم رصيد يصل إلى -5 أيام."

#. module: hr_holidays_negative_cap
#: model:ir.model.fields,field_description:hr_holidays_negative_cap.field_hr_leave__effective_remaining_days
msgid "Effective Remaining Days"
msgstr "الأيام المتبقية الفعلية"

#. module: hr_holidays_negative_cap
#: model:ir.model.fields,help:hr_holidays_negative_cap.field_hr_leave__effective_remaining_days
msgid "Remaining days including negative cap allowance"
msgstr "الأيام المتبقية بما في ذلك بدل السقف السلبي"

#. module: hr_holidays_negative_cap
#: model:ir.model.fields,field_description:hr_holidays_negative_cap.field_hr_leave__will_use_negative_cap
msgid "Will Use Negative Cap"
msgstr "سيستخدم السقف السلبي"

#. module: hr_holidays_negative_cap
#: model:ir.model.fields,help:hr_holidays_negative_cap.field_hr_leave__will_use_negative_cap
msgid "Indicates if this request will result in negative balance"
msgstr "يشير إلى ما إذا كان هذا الطلب سيؤدي إلى رصيد سلبي"

#. module: hr_holidays_negative_cap
#: model:ir.ui.view,arch_db:hr_holidays_negative_cap.hr_leave_type_view_form_negative_cap
msgid "Negative Cap Configuration"
msgstr "إعداد السقف السلبي"

#. module: hr_holidays_negative_cap
#: model:ir.ui.view,arch_db:hr_holidays_negative_cap.hr_leave_view_form_negative_cap
msgid "Negative Cap Enabled:"
msgstr "السقف السلبي مفعل:"

#. module: hr_holidays_negative_cap
#: model:ir.ui.view,arch_db:hr_holidays_negative_cap.hr_leave_view_form_negative_cap
msgid "negative balance allowed for this leave type."
msgstr "رصيد سلبي مسموح لهذا النوع من الإجازة."

#. module: hr_holidays_negative_cap
#: model:ir.ui.view,arch_db:hr_holidays_negative_cap.hr_leave_view_form_negative_cap
msgid "Warning:"
msgstr "تحذير:"

#. module: hr_holidays_negative_cap
#: model:ir.ui.view,arch_db:hr_holidays_negative_cap.hr_leave_view_form_negative_cap
msgid "This request will result in negative balance."
msgstr "هذا الطلب سيؤدي إلى رصيد سلبي."

#. module: hr_holidays_negative_cap
#: model:ir.ui.view,arch_db:hr_holidays_negative_cap.hr_leave_view_search_negative_cap
msgid "Using Negative Cap"
msgstr "يستخدم السقف السلبي"

#. module: hr_holidays_negative_cap
#: model:ir.ui.view,arch_db:hr_holidays_negative_cap.hr_leave_view_search_negative_cap
msgid "Negative Cap Enabled"
msgstr "السقف السلبي مفعل"

#. module: hr_holidays_negative_cap
#: code:addons/hr_holidays_negative_cap/models/hr_leave_type.py:0
#, python-format
msgid "Negative cap days must be a positive number or zero. It represents the maximum negative balance allowed."
msgstr "أيام السقف السلبي يجب أن تكون رقماً موجباً أو صفراً. تمثل الحد الأقصى للرصيد السلبي المسموح."

#. module: hr_holidays_negative_cap
#: code:addons/hr_holidays_negative_cap/models/hr_leave.py:0
#, python-format
msgid "Cannot approve this leave request. Employee %(employee)s would exceed the negative cap limit of %(limit)s days for leave type \"%(leave_type)s\". Current balance: %(current)s days, Requested: %(requested)s days, Balance after request: %(after)s days."
msgstr "لا يمكن الموافقة على طلب الإجازة هذا. الموظف %(employee)s سيتجاوز حد السقف السلبي البالغ %(limit)s أيام لنوع الإجازة \"%(leave_type)s\". الرصيد الحالي: %(current)s أيام، المطلوب: %(requested)s أيام، الرصيد بعد الطلب: %(after)s أيام."

#. module: hr_holidays_negative_cap
#: code:addons/hr_holidays_negative_cap/models/hr_leave.py:0
#, python-format
msgid "Cannot approve this leave request. Employee %(employee)s does not have sufficient balance for leave type \"%(leave_type)s\". Current balance: %(current)s days, Requested: %(requested)s days."
msgstr "لا يمكن الموافقة على طلب الإجازة هذا. الموظف %(employee)s ليس لديه رصيد كافي لنوع الإجازة \"%(leave_type)s\". الرصيد الحالي: %(current)s أيام، المطلوب: %(requested)s أيام."

#. module: hr_holidays_negative_cap
#: code:addons/hr_holidays_negative_cap/models/hr_leave.py:0
#, python-format
msgid "Warning: The following leave requests will result in negative balance:\\n"
msgstr "تحذير: طلبات الإجازة التالية ستؤدي إلى رصيد سلبي:\\n"

#. module: hr_holidays_negative_cap
#: code:addons/hr_holidays_negative_cap/models/hr_leave.py:0
#, python-format
msgid "- %s: %s days for %s\\n"
msgstr "- %s: %s أيام لـ %s\\n"

#. module: hr_holidays_negative_cap
#: code:addons/hr_holidays_negative_cap/models/hr_leave.py:0
#, python-format
msgid "This leave request will result in negative balance. Negative cap is enabled for this leave type."
msgstr "طلب الإجازة هذا سيؤدي إلى رصيد سلبي. السقف السلبي مفعل لهذا النوع من الإجازة."
