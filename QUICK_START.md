# 🚀 دليل البدء السريع - مديوول السقف السلبي للإجازات
# Quick Start Guide - HR Holidays Negative Cap Module

## ⚡ البدء السريع | Quick Start

### 1️⃣ التثبيت السريع | Quick Installation
```bash
# انسخ المديوول إلى مجلد addons
cp -r hr_holidays_negative_cap /path/to/odoo/addons/

# أعد تشغيل Odoo
sudo systemctl restart odoo

# في واجهة Odoo:
# Apps > Update Apps List > Search "HR Holidays Negative Cap" > Install
```

### 2️⃣ الإعداد السريع | Quick Configuration
1. **اذهب إلى**: Time Off > Configuration > Time Off Types
2. **اختر نوع الإجازة** (مثل: Annual Leave)
3. **فعّل**: ✅ Allow Negative Cap
4. **حدد**: 📊 Negative Cap Days = 5
5. **احفظ** ✅

### 3️⃣ الاستخدام السريع | Quick Usage
- **للموظف**: اطلب إجازة كالمعتاد - سيظهر تحذير إذا كان سيستخدم رصيد سلبي
- **للمدير**: وافق على الطلبات - النظام سيمنع تجاوز الحد المسموح
- **لـ HR**: استخدم فلتر "Using Negative Cap" لمراقبة الاستخدام

## 📋 أمثلة سريعة | Quick Examples

### مثال 1: إعداد الإجازة السنوية
```
نوع الإجازة: Annual Leave
✅ Allow Negative Cap: مفعل
📊 Negative Cap Days: 5 أيام
📝 المعنى: يمكن للموظف رصيد -5 أيام كحد أقصى
```

### مثال 2: طلب إجازة
```
رصيد الموظف الحالي: 3 أيام
طلب إجازة: 7 أيام
النتيجة: سيصبح الرصيد -4 أيام (مقبول لأنه أقل من -5)
```

### مثال 3: طلب مرفوض
```
رصيد الموظف الحالي: 2 أيام
طلب إجازة: 10 أيام
النتيجة: مرفوض (سيصبح الرصيد -8 أيام، يتجاوز حد -5)
```

## 🎯 الميزات الرئيسية | Key Features

| الميزة | الوصف | المثال |
|--------|--------|---------|
| 🔧 **إعداد مرن** | حد مختلف لكل نوع إجازة | Annual: -5، Sick: -3 |
| ⚠️ **تحذيرات ذكية** | تنبيه عند استخدام الرصيد السلبي | "سيؤدي لرصيد سلبي" |
| 🛡️ **حماية البيانات** | منع تجاوز الحدود المحددة | رفض الطلبات المتجاوزة |
| 🔍 **مراقبة سهلة** | فلاتر للطلبات السلبية | "Using Negative Cap" |

## 🚨 تحذيرات مهمة | Important Warnings

### ⚠️ قبل التثبيت
- تأكد من عمل نسخة احتياطية من قاعدة البيانات
- اختبر المديوول في بيئة تجريبية أولاً
- تأكد من توافق إصدار Odoo

### ⚠️ بعد التثبيت
- راجع إعدادات أنواع الإجازات الموجودة
- اضبط الحدود السلبية حسب سياسة الشركة
- درّب المستخدمين على الميزات الجديدة

## 🔧 استكشاف الأخطاء السريع | Quick Troubleshooting

### ❌ المشكلة: لا تظهر الحقول الجديدة
**✅ الحل**: 
```bash
# أعد تحميل الصفحة
Ctrl + F5
# أو أعد تشغيل Odoo
sudo systemctl restart odoo
```

### ❌ المشكلة: خطأ في التثبيت
**✅ الحل**:
```bash
# تحقق من الاختبارات
cd hr_holidays_negative_cap
python quick_test.py
```

### ❌ المشكلة: لا يعمل التحقق من الصحة
**✅ الحل**:
- تأكد من حفظ إعدادات نوع الإجازة
- تحقق من تفعيل "Allow Negative Cap"
- راجع قيمة "Negative Cap Days"

## 📞 الدعم السريع | Quick Support

### 📚 المراجع السريعة
- `README.md` - الدليل الشامل
- `INSTALLATION.md` - دليل التثبيت المفصل
- `SUMMARY.md` - ملخص المديوول

### 🧪 الاختبار السريع
```bash
cd hr_holidays_negative_cap
python quick_test.py
```

### 🔧 الإعداد التلقائي
```bash
chmod +x setup_guide.sh
./setup_guide.sh
```

## 🎉 تهانينا!

إذا وصلت إلى هنا، فقد نجحت في إعداد مديوول السقف السلبي للإجازات! 

الآن يمكن لموظفيك الاستفادة من مرونة أكبر في طلب الإجازات مع الحفاظ على الضوابط اللازمة.

---

**💡 نصيحة**: ابدأ بحدود صغيرة (مثل 2-3 أيام) ثم اضبطها حسب احتياجات شركتك.
