# 🔧 دليل استكشاف الأخطاء - مديوول السقف السلبي للإجازات
# Troubleshooting Guide - HR Holidays Negative Cap Module

## 🚨 المشاكل الشائعة وحلولها | Common Issues and Solutions

### 1. خطأ XML Parse Error

#### ❌ المشكلة:
```
ParseError: while parsing /path/to/hr_holidays_negative_cap/views/hr_leave_type_views.xml
```

#### ✅ الحل:
```bash
# تحقق من صحة ملف XML
cd hr_holidays_negative_cap
python -c "import xml.etree.ElementTree as ET; ET.parse('views/hr_leave_type_views.xml'); print('XML is valid')"

# إذا كان هناك خطأ، تحقق من:
# 1. إغلاق جميع العلامات بشكل صحيح
# 2. استخدام الاقتباسات بشكل صحيح
# 3. عدم وجود أحرف خاصة غير مُرمزة
```

### 2. لا تظهر الحقول الجديدة

#### ❌ المشكلة:
الحقول `allow_negative_cap` و `negative_cap_days` لا تظهر في واجهة نوع الإجازة.

#### ✅ الحل:
```bash
# 1. تأكد من تثبيت المديوول
# 2. أعد تحميل الصفحة
Ctrl + F5

# 3. تحقق من الصلاحيات
# تأكد أن المستخدم لديه صلاحية "Time Off Officer" أو "Manager"

# 4. أعد تشغيل Odoo
sudo systemctl restart odoo

# 5. حدث المديوول
# Apps > HR Holidays Negative Cap > Upgrade
```

### 3. خطأ في التحقق من الصحة

#### ❌ المشكلة:
```
ValidationError: Negative cap days must be a positive number or zero
```

#### ✅ الحل:
```python
# تأكد من أن قيمة Negative Cap Days موجبة أو صفر
# مثال صحيح:
allow_negative_cap = True
negative_cap_days = 5.0  # ✅ صحيح

# مثال خاطئ:
negative_cap_days = -3.0  # ❌ خاطئ
```

### 4. لا يعمل منع تجاوز الحد

#### ❌ المشكلة:
النظام يسمح بطلبات تتجاوز الحد السلبي المحدد.

#### ✅ الحل:
```bash
# 1. تحقق من إعدادات نوع الإجازة
# - تأكد أن Allow Negative Cap مفعل
# - تأكد أن Negative Cap Days محدد بشكل صحيح

# 2. تحقق من حالة الطلب
# التحقق يحدث فقط عند الموافقة (state = 'validate')

# 3. تحقق من رصيد الموظف
# تأكد من وجود تخصيص (allocation) للموظف
```

### 5. رسائل الخطأ باللغة الإنجليزية

#### ❌ المشكلة:
رسائل الخطأ والتحذيرات تظهر باللغة الإنجليزية.

#### ✅ الحل:
```bash
# 1. تفعيل اللغة العربية
# Settings > Translations > Languages > Arabic > Activate

# 2. تحديث ملفات الترجمة
# Settings > Translations > Load a Translation
# اختر Arabic وحدد المديوولات

# 3. تغيير لغة المستخدم
# User Profile > Preferences > Language > Arabic
```

### 6. خطأ في الاستيراد

#### ❌ المشكلة:
```
ImportError: No module named 'hr_holidays_negative_cap'
```

#### ✅ الحل:
```bash
# 1. تحقق من مسار المديوول
ls /path/to/odoo/addons/hr_holidays_negative_cap

# 2. تحقق من ملف __init__.py
cat hr_holidays_negative_cap/__init__.py

# 3. أعد تشغيل Odoo
sudo systemctl restart odoo

# 4. تحديث قائمة التطبيقات
# Apps > Update Apps List
```

### 7. خطأ في قاعدة البيانات

#### ❌ المشكلة:
```
ProgrammingError: column "allow_negative_cap" does not exist
```

#### ✅ الحل:
```bash
# 1. حدث المديوول
# Apps > HR Holidays Negative Cap > Upgrade

# 2. إذا لم يعمل، أعد تثبيت المديوول
# Apps > HR Holidays Negative Cap > Uninstall
# ثم Install مرة أخرى

# 3. تحقق من سجلات Odoo
tail -f /var/log/odoo/odoo.log
```

### 8. مشاكل الأداء

#### ❌ المشكلة:
بطء في تحميل واجهات الإجازات.

#### ✅ الحل:
```python
# 1. تحقق من الفهارس في قاعدة البيانات
# 2. راجع الاستعلامات المعقدة
# 3. استخدم التخزين المؤقت للحقول المحسوبة

# مثال على تحسين الأداء:
@api.depends('employee_id', 'holiday_status_id')
def _compute_effective_remaining_days(self):
    # استخدام batch processing
    for leave in self:
        # منطق محسن...
```

## 🔍 أدوات التشخيص | Diagnostic Tools

### اختبار سريع للمديوول
```bash
cd hr_holidays_negative_cap
python quick_test.py
```

### فحص ملفات XML
```bash
# فحص جميع ملفات XML
find . -name "*.xml" -exec python -c "import xml.etree.ElementTree as ET; ET.parse('{}'); print('✅ {} is valid')" \;
```

### فحص ملفات Python
```bash
# فحص صحة Python syntax
find . -name "*.py" -exec python -m py_compile {} \;
```

### فحص الصلاحيات
```bash
# تحقق من ملف الصلاحيات
cat security/ir.model.access.csv
```

## 📊 مراقبة الأداء | Performance Monitoring

### مراقبة استخدام الذاكرة
```python
# في Python console
import psutil
process = psutil.Process()
print(f"Memory usage: {process.memory_info().rss / 1024 / 1024:.2f} MB")
```

### مراقبة استعلامات قاعدة البيانات
```python
# تفعيل debug mode
# في ملف الإعداد:
# log_level = debug
# log_db_level = debug
```

## 🆘 طلب المساعدة | Getting Help

### معلومات مطلوبة عند طلب المساعدة:
1. **إصدار Odoo**: `python odoo-bin --version`
2. **رسالة الخطأ الكاملة**: نسخ كامل من سجل الأخطاء
3. **خطوات إعادة الإنتاج**: كيفية حدوث المشكلة
4. **إعدادات المديوول**: لقطة شاشة من إعدادات نوع الإجازة
5. **بيئة النظام**: نظام التشغيل، Python version، إلخ

### ملفات السجلات المهمة:
```bash
# سجل Odoo الرئيسي
tail -f /var/log/odoo/odoo.log

# سجل خادم الويب
tail -f /var/log/nginx/error.log  # أو Apache

# سجل قاعدة البيانات
tail -f /var/log/postgresql/postgresql.log
```

## ✅ قائمة التحقق السريع | Quick Checklist

قبل طلب المساعدة، تأكد من:

- [ ] تم تثبيت المديوول بنجاح
- [ ] تم إعادة تشغيل Odoo بعد التثبيت
- [ ] تم تحديث قائمة التطبيقات
- [ ] المستخدم لديه الصلاحيات المناسبة
- [ ] إعدادات نوع الإجازة صحيحة
- [ ] لا توجد تعارضات مع مديوولات أخرى
- [ ] تم اختبار المديوول في بيئة تجريبية
- [ ] تم عمل نسخة احتياطية من قاعدة البيانات

---

**💡 نصيحة**: احتفظ بهذا الدليل في مكان سهل الوصول للرجوع إليه عند الحاجة.
