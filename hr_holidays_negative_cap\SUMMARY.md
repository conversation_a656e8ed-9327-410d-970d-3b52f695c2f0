# ملخص مديوول السقف السلبي للإجازات
# HR Holidays Negative Cap Module Summary

## 🎯 الهدف من المديوول | Module Purpose

يهدف هذا المديوول إلى إضافة خاصية "السقف السلبي" إلى نظام الإجازات في Odoo، مما يسمح للموظفين بأخذ إجازات تتجاوز رصيدهم المتاح بحد أقصى محدد مسبقاً.

This module adds "Negative Cap" functionality to Odoo's Time Off system, allowing employees to take leave beyond their available balance up to a predefined limit.

## 📋 الملفات المنشأة | Created Files

### 🔧 الملفات الأساسية | Core Files
- `__manifest__.py` - ملف تعريف المديوول
- `__init__.py` - ملف التهيئة الرئيسي
- `models/__init__.py` - تهيئة النماذج
- `models/hr_leave_type.py` - توسيع نموذج نوع الإجازة
- `models/hr_leave.py` - توسيع نموذج طلب الإجازة

### 🎨 ملفات الواجهة | Interface Files
- `views/hr_leave_type_views.xml` - واجهات نوع الإجازة
- `security/ir.model.access.csv` - صلاحيات الوصول

### 🧪 ملفات الاختبار | Testing Files
- `tests/__init__.py` - تهيئة الاختبارات
- `tests/test_negative_cap.py` - اختبارات شاملة للمديوول

### 📚 ملفات التوثيق | Documentation Files
- `README.md` - دليل شامل للمديوول
- `INSTALLATION.md` - دليل التثبيت والإعداد
- `CHANGELOG.md` - سجل التغييرات
- `SUMMARY.md` - هذا الملف

### 🌐 ملفات الترجمة | Translation Files
- `i18n/ar.po` - الترجمة العربية

### 🛠️ ملفات المساعدة | Utility Files
- `quick_test.py` - اختبار سريع للمديوول
- `setup_guide.sh` - دليل الإعداد التلقائي

## ⚙️ الميزات الرئيسية | Key Features

### 1. إعداد السقف السلبي | Negative Cap Configuration
- ✅ تفعيل/إلغاء تفعيل السقف السلبي لكل نوع إجازة
- 📊 تحديد عدد الأيام السلبية المسموح بها (مثل: 0، 5، 10 أيام)
- 🔧 إعدادات مرنة لكل نوع إجازة منفصل

### 2. التحقق من الصحة | Validation
- 🛡️ منع الطلبات التي تتجاوز الحد المسموح
- 📝 رسائل خطأ واضحة ومفصلة
- ✅ الحفاظ على سلامة البيانات

### 3. واجهة المستخدم | User Interface
- 💡 مؤشرات بصرية عند تفعيل السقف السلبي
- ⚠️ تحذيرات عند استخدام الرصيد السلبي
- 🔍 فلاتر بحث للطلبات السلبية
- 📊 أعمدة إضافية في القوائم

### 4. المراقبة والتتبع | Monitoring & Tracking
- 📈 تتبع استخدام السقف السلبي
- 📝 رسائل تلقائية عند الاستخدام
- 🔍 تقارير وإحصائيات

## 🎛️ كيفية الاستخدام | How to Use

### للمديرين | For Managers
1. اذهب إلى: Time Off > Configuration > Time Off Types
2. اختر نوع الإجازة المراد تكوينه
3. فعّل "Allow Negative Cap"
4. حدد "Negative Cap Days" (مثال: 5 أيام)
5. احفظ الإعدادات

### للموظفين | For Employees
1. اطلب الإجازة كالمعتاد
2. إذا كان الطلب سيؤدي لرصيد سلبي، ستظهر رسالة تحذيرية
3. سيتم قبول الطلب إذا كان ضمن الحد المسموح

### لموظفي الموارد البشرية | For HR Officers
1. راقب الطلبات السلبية باستخدام الفلاتر
2. راجع التقارير والإحصائيات
3. اضبط الحدود حسب الحاجة

## 🔧 التثبيت | Installation

### الخطوات | Steps
1. انسخ مجلد `hr_holidays_negative_cap` إلى مجلد addons
2. أعد تشغيل Odoo
3. حدّث قائمة التطبيقات
4. ثبّت المديوول "HR Holidays Negative Cap"

### التحقق من التثبيت | Installation Verification
```bash
cd hr_holidays_negative_cap
python quick_test.py
```

## 📊 أمثلة على الاستخدام | Usage Examples

### مثال 1: الإجازة السنوية | Annual Leave
- الرصيد الحالي: 10 أيام
- السقف السلبي: 5 أيام
- الحد الأقصى للطلب: 15 يوم (10 + 5)

### مثال 2: الإجازة المرضية | Sick Leave
- الرصيد الحالي: 3 أيام
- السقف السلبي: 2 أيام
- الحد الأقصى للطلب: 5 أيام (3 + 2)

### مثال 3: إجازة بدون سقف سلبي | Leave without Negative Cap
- الرصيد الحالي: 5 أيام
- السقف السلبي: غير مفعل
- الحد الأقصى للطلب: 5 أيام فقط

## 🔒 الأمان | Security

- ✅ صلاحيات وصول محددة لكل دور
- 🛡️ تحقق من صحة البيانات على مستوى الخادم
- 📝 تسجيل جميع العمليات للمراجعة

## 🚀 الأداء | Performance

- ⚡ استعلامات قاعدة بيانات محسّنة
- 💾 حقول محسوبة ذكية
- 🔄 حمولة إضافية قليلة على النظام

## 🌐 الدعم متعدد اللغات | Multi-language Support

- 🇸🇦 العربية (مكتملة)
- 🇺🇸 الإنجليزية (مكتملة)
- 🌍 قابل للتوسع لغات أخرى

## 📞 الدعم الفني | Technical Support

للحصول على الدعم الفني أو طلب تخصيصات إضافية:
- راجع ملف README.md للتفاصيل الكاملة
- راجع ملف INSTALLATION.md لدليل التثبيت
- استخدم quick_test.py للتحقق من سلامة المديوول

## 📈 التطوير المستقبلي | Future Development

- 📧 إشعارات بريد إلكتروني
- 📊 تقارير متقدمة
- 📱 تحسين الواجهة للهواتف المحمولة
- 🔗 تكامل أفضل مع مديوولات الرواتب

---

**تم إنشاء هذا المديوول بعناية لضمان الجودة والموثوقية والسهولة في الاستخدام.**

**This module was carefully crafted to ensure quality, reliability, and ease of use.**
