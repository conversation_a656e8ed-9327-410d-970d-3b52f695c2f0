#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick test script for HR Holidays Negative Cap module
Run this script to verify the module is working correctly
"""

def test_module_structure():
    """Test that all required files exist"""
    import os
    
    required_files = [
        '__init__.py',
        '__manifest__.py',
        'models/__init__.py',
        'models/hr_leave_type.py',
        'models/hr_leave.py',
        'views/hr_leave_type_views.xml',
        'security/ir.model.access.csv',
        'tests/__init__.py',
        'tests/test_negative_cap.py',
        'README.md',
        'INSTALLATION.md'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ All required files exist")
        return True

def test_manifest_structure():
    """Test manifest file structure"""
    try:
        with open('__manifest__.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_keys = ['name', 'version', 'depends', 'data']
        for key in required_keys:
            if f"'{key}'" not in content:
                print(f"❌ Missing key in manifest: {key}")
                return False
        
        print("✅ Manifest file structure is correct")
        return True
    except Exception as e:
        print(f"❌ Error reading manifest: {e}")
        return False

def test_python_syntax():
    """Test Python files for syntax errors"""
    import ast
    
    python_files = [
        '__init__.py',
        'models/__init__.py',
        'models/hr_leave_type.py',
        'models/hr_leave.py',
        'tests/__init__.py',
        'tests/test_negative_cap.py'
    ]
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            ast.parse(content)
        except SyntaxError as e:
            print(f"❌ Syntax error in {file_path}: {e}")
            return False
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            return False
    
    print("✅ All Python files have valid syntax")
    return True

def test_xml_structure():
    """Test XML files for basic structure"""
    import xml.etree.ElementTree as ET
    
    xml_files = ['views/hr_leave_type_views.xml']
    
    for file_path in xml_files:
        try:
            ET.parse(file_path)
        except ET.ParseError as e:
            print(f"❌ XML parse error in {file_path}: {e}")
            return False
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            return False
    
    print("✅ All XML files have valid structure")
    return True

def main():
    """Run all tests"""
    print("🧪 Testing HR Holidays Negative Cap Module")
    print("=" * 50)
    
    tests = [
        test_module_structure,
        test_manifest_structure,
        test_python_syntax,
        test_xml_structure
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
        print()
    
    if all_passed:
        print("🎉 All tests passed! Module is ready for installation.")
        print("\nNext steps:")
        print("1. Copy the module to your Odoo addons directory")
        print("2. Restart Odoo")
        print("3. Update Apps List")
        print("4. Install 'HR Holidays Negative Cap' module")
    else:
        print("❌ Some tests failed. Please fix the issues before installation.")

if __name__ == '__main__':
    main()
