# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class HrLeaveType(models.Model):
    _inherit = 'hr.leave.type'

    # Negative Cap Configuration
    allow_negative_cap = fields.Boolean(
        string='Allow Negative Cap',
        default=False,
        help='Allow employees to take time off even when they don\'t have sufficient balance, '
             'up to the specified negative limit.'
    )
    
    negative_cap_days = fields.Float(
        string='Negative Cap (Days)',
        default=0.0,
        help='Maximum number of days that can be taken as negative balance. '
             'For example, if set to 5, employees can have up to -5 days balance.'
    )

    @api.constrains('allow_negative_cap', 'negative_cap_days')
    def _check_negative_cap_configuration(self):
        """Validate negative cap configuration"""
        for record in self:
            if record.allow_negative_cap and record.negative_cap_days < 0:
                raise ValidationError(_(
                    'Negative cap days must be a positive number or zero. '
                    'It represents the maximum negative balance allowed.'
                ))
            
            if not record.allow_negative_cap and record.negative_cap_days > 0:
                # Reset negative cap days if negative cap is disabled
                record.negative_cap_days = 0.0

    @api.onchange('allow_negative_cap')
    def _onchange_allow_negative_cap(self):
        """Reset negative cap days when negative cap is disabled"""
        if not self.allow_negative_cap:
            self.negative_cap_days = 0.0

    def get_employees_days(self, employee_ids, date=None):
        """
        Override to consider negative cap when calculating available days
        """
        result = super().get_employees_days(employee_ids, date)
        
        # Add negative cap information to the result
        for leave_type_id, employees_data in result.items():
            leave_type = self.browse(leave_type_id)
            if leave_type.allow_negative_cap:
                for employee_id, data in employees_data.items():
                    # Add negative cap information
                    data['negative_cap_allowed'] = leave_type.negative_cap_days
                    data['allow_negative_cap'] = True
                    
                    # Calculate effective remaining days including negative cap
                    current_remaining = data.get('remaining_leaves', 0)
                    data['effective_remaining'] = current_remaining + leave_type.negative_cap_days
            else:
                for employee_id, data in employees_data.items():
                    data['negative_cap_allowed'] = 0.0
                    data['allow_negative_cap'] = False
                    data['effective_remaining'] = data.get('remaining_leaves', 0)
        
        return result
