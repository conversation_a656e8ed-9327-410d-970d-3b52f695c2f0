<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Time Off Type Form View to Add Negative Cap Configuration -->
    <record id="hr_leave_type_view_form_negative_cap" model="ir.ui.view">
        <field name="name">hr.leave.type.form.negative.cap</field>
        <field name="model">hr.leave.type</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_type_view_form"/>
        <field name="arch" type="xml">
            <!-- Add Negative Cap section after the allocation validation group -->
            <xpath expr="//group[@name='allocation_validation']" position="after">
                <group string="Negative Cap Configuration" name="negative_cap_config">
                    <field name="allow_negative_cap" widget="boolean_toggle"/>
                    <field name="negative_cap_days"
                           attrs="{'invisible': [('allow_negative_cap', '=', False)],
                                   'required': [('allow_negative_cap', '=', True)]}"
                           widget="float_time"/>
                </group>
            </xpath>
        </field>
    </record>

    <!-- Add Negative Cap fields to Tree View for quick reference -->
    <record id="hr_leave_type_view_tree_negative_cap" model="ir.ui.view">
        <field name="name">hr.leave.type.tree.negative.cap</field>
        <field name="model">hr.leave.type</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_type_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='requires_allocation']" position="after">
                <field name="allow_negative_cap" optional="hide"/>
                <field name="negative_cap_days" optional="hide" widget="float_time"/>
            </xpath>
        </field>
    </record>

    <!-- Inherit Leave Request Form View to Show Negative Cap Information -->
    <record id="hr_leave_view_form_negative_cap" model="ir.ui.view">
        <field name="name">hr.leave.form.negative.cap</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_form"/>
        <field name="arch" type="xml">
            <!-- Add negative cap information in the duration section -->
            <xpath expr="//div[@name='duration_display']" position="after">
                <div class="o_row" attrs="{'invisible': [('allow_negative_cap', '=', False)]}">
                    <field name="allow_negative_cap" invisible="1"/>
                    <field name="negative_cap_days" invisible="1"/>
                    <field name="effective_remaining_days" invisible="1"/>
                    <field name="will_use_negative_cap" invisible="1"/>
                    
                    <div class="alert alert-info" role="alert" 
                         attrs="{'invisible': [('allow_negative_cap', '=', False)]}">
                        <i class="fa fa-info-circle"/> 
                        <strong>Negative Cap Enabled:</strong> 
                        Up to <field name="negative_cap_days" widget="float_time" readonly="1"/> 
                        negative balance allowed for this leave type.
                    </div>
                    
                    <div class="alert alert-warning" role="alert" 
                         attrs="{'invisible': [('will_use_negative_cap', '=', False)]}">
                        <i class="fa fa-exclamation-triangle"/> 
                        <strong>Warning:</strong> 
                        This request will result in negative balance.
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <!-- Inherit Leave Request Tree View to Show Negative Cap Status -->
    <record id="hr_leave_view_tree_negative_cap" model="ir.ui.view">
        <field name="name">hr.leave.tree.negative.cap</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.hr_leave_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='number_of_days']" position="after">
                <field name="will_use_negative_cap" optional="hide" widget="boolean_toggle"/>
            </xpath>
        </field>
    </record>

    <!-- Add Search Filter for Negative Cap Requests -->
    <record id="hr_leave_view_search_negative_cap" model="ir.ui.view">
        <field name="name">hr.leave.search.negative.cap</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.view_hr_leave_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='approved']" position="after">
                <separator/>
                <filter string="Using Negative Cap" name="negative_cap_used" 
                        domain="[('will_use_negative_cap', '=', True)]"/>
                <filter string="Negative Cap Enabled" name="negative_cap_enabled" 
                        domain="[('allow_negative_cap', '=', True)]"/>
            </xpath>
        </field>
    </record>
</odoo>
